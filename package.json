{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vue-tsc && vite build", "dev": "vite", "lint": "eslint resources/js --ext .js,.ts,.vue --ignore-path .gitignore --fix"}, "devDependencies": {"@inertiajs/vue3": "^2.0.0", "@rushstack/eslint-patch": "^1.8.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "autoprefixer": "^10.4.12", "axios": "^1.11.0", "concurrently": "^9.0.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.4.31", "prettier": "^3.3.0", "prettier-plugin-organize-imports": "^4.0.0", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.2.1", "typescript": "^5.6.3", "vite": "^7.0.4", "vue": "^3.4.0", "vue-tsc": "^2.0.24"}}